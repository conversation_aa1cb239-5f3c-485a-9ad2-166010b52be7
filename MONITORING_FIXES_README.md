# 监控告警页面修复说明

## 修复概述

本次修复解决了监控告警页面中的关键问题，并实现了原始标签值显示：

1. **Token相关指标的标签项数量问题** - 确保图例标签项数量与实际数据线条数量完全匹配
2. **服务维度TCP连接数显示异常** - 修复服务名提取和分组显示问题
3. **原始标签值显示** - 图表图例直接使用Prometheus查询中`sum by()`或`group by()`括号内的原始标签名称

## 问题详情

### 问题1：Token相关指标标签项数量不匹配

**现象**：
- 所有Token相关的监控指标图表中，图例标签项的数量与实际显示的数据线条数量不匹配
- 用户期望每条数据线都有对应的标签项，标签数量应该与Prometheus查询返回的时间序列数量完全一致

**根本原因**：
- 数据处理过程中存在多次标签清理和处理步骤
- `processAiClusterLabelsInSeriesData` 和 `processSeriesDataLabels` 的重复调用可能导致数据丢失或重复处理
- 标签清理逻辑过于复杂，影响了数据完整性

### 问题2：服务维度TCP连接数显示异常

**现象**：
- TCP连接数按服务维度分组显示时出现异常
- 期望按服务名称（service_name）进行分组，每个服务显示为独立的数据线
- 当前显示的是当前活跃TCP连接数

**根本原因**：
- Prometheus查询使用了复杂的`label_replace`函数，但正则表达式可能不够准确
- 服务名提取逻辑无法正确处理所有cluster_name格式
- 标签处理器缺少对cluster_name标签的支持

## 修复方案

### 修复1：实现原始标签值显示

**修改文件**：
- `src/pages/MonitorAlert/components/AlertChart.tsx`
- `src/pages/MonitorAlert/components/MultiSeriesChart.tsx`

**主要改动**：
1. **完全移除标签处理逻辑**：禁用所有标签清理函数，如`cleanLabelName()`、`extractServiceNameFromAiCluster()`等
2. **直接使用原始标签值**：图表图例直接显示Prometheus metric对象中的原始标签值
3. **确保标签完整性**：保持标签的完整性和原始性，便于用户识别具体的监控目标
4. **保证数量一致性**：确保图例标签项数量与Prometheus返回的时间序列数量完全一致

**修复前流程**：
```
原始数据 → 复杂标签处理 → 服务名提取 → 标签清理 → 最终显示名称
```

**修复后流程**：
```
原始数据 → 直接提取第一个标签的原始值 → 最终显示名称
```

**具体实现**：
- 对于使用`sum by (ai_route)`的查询，图例直接显示`ai_route`的原始值
- 对于使用`sum by (ai_cluster)`的查询，图例直接显示`ai_cluster`的原始值
- 对于使用`sum by (cluster_name)`的查询，图例直接显示`cluster_name`的原始值
- **对于多标签查询**（如`sum by (ai_cluster, ai_consumer)`），图例显示所有标签值的组合，用` | `分隔

### 修复2：优化TCP连接数查询和服务名提取

**修改文件**：
- `src/pages/MonitorAlert/index.tsx` - 简化Prometheus查询
- `src/utils/serviceNameExtractor.ts` - 增强服务名提取逻辑
- `src/utils/labelCleaner.ts` - 添加cluster_name支持

**主要改动**：

1. **简化Prometheus查询**：
   ```promql
   # 修复前（复杂的label_replace）
   sum by (destination_service_name) (
     label_replace(
       envoy_cluster_upstream_cx_active{...},
       "destination_service_name", "$1", "cluster_name", "outbound\\\\|[^|]*\\\\|[^|]*\\\\|([^.]+)\\\\..*"
     )
   )
   
   # 修复后（简化查询，在前端处理）
   sum by (cluster_name) (
     envoy_cluster_upstream_cx_active{
       user_namespace="$namespace",
       cluster_name=~"^outbound.*",
       cluster_name!~".*redis.*"
     }
   )
   ```

2. **增强服务名提取逻辑**：
   - 支持更多cluster_name格式
   - 处理空字段的情况（如`outbound|8379||redis-service.redis-aigw.svc.cluster.local`）
   - 改进服务名清理逻辑

3. **添加cluster_name标签支持**：
   - 在标签清理器中添加`cluster_name:`前缀支持
   - 在图表组件中添加cluster_name处理逻辑

## 测试验证

### 测试工具

创建了专门的测试工具 `src/utils/testMonitoringFixes.ts`，包含以下测试功能：

1. **服务名提取测试** - 验证各种cluster_name格式的服务名提取
2. **标签清理测试** - 验证标签前缀清理功能
3. **cluster标签识别测试** - 验证cluster格式标签的识别
4. **Token指标数据处理测试** - 模拟Token指标的完整处理流程
5. **TCP连接数数据处理测试** - 模拟TCP连接数的完整处理流程

### 使用方法

在浏览器控制台中运行：
```javascript
// 运行原始标签值显示测试
window.testRawLabels.runAllRawLabelTests();

// 测试原始标签值提取逻辑
window.testRawLabels.testRawLabelExtraction();

// 测试标签数量一致性
window.testRawLabels.testLabelCountConsistency();

// 测试完整的数据处理流程
window.testRawLabels.testCompleteDataProcessing();

// 专门测试多标签查询（解决消费者Token统计问题）
window.testRawLabels.testMultiLabelQueries();

// 运行旧版监控修复测试（用于对比）
window.testMonitoringFixes.runAllTests();
```

### 测试用例

**原始标签值显示测试用例**：

**单标签查询**：
- `ai_route: 'chat-completion'` → 图例显示：`chat-completion`
- `ai_cluster: 'outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local'` → 图例显示：`outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local`
- `cluster_name: 'outbound|8379||redis-service.redis-aigw.svc.cluster.local'` → 图例显示：`outbound|8379||redis-service.redis-aigw.svc.cluster.local`
- `response_code: '200'` → 图例显示：`200`

**多标签查询**（如`sum by (ai_cluster, ai_consumer)`）：
- `{ai_cluster: 'outbound|80|user-ollama-service-v1|...', ai_consumer: 'consumer'}` → 图例显示：`outbound|80|user-ollama-service-v1|... | consumer`
- `{ai_cluster: 'outbound|80|user-ollama-service-v1|...', ai_consumer: 'consumer2'}` → 图例显示：`outbound|80|user-ollama-service-v1|... | consumer2`

## 预期效果

### 修复后的预期表现

1. **所有监控指标**：
   - 图例标签项数量与数据线条数量完全匹配
   - 每个Prometheus时间序列都有对应的图例项
   - **图例直接显示原始标签值**，不进行任何处理

2. **具体显示效果**：
   - **ai_route指标**：图例显示`chat-completion`、`embeddings`、`text-generation`等原始路由名称
   - **ai_cluster指标**：图例显示完整的cluster字符串，如`outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local`
   - **cluster_name指标**：图例显示完整的cluster_name字符串，如`outbound|8379||redis-service.redis-aigw.svc.cluster.local`
   - **response_code指标**：图例显示原始响应码，如`200`、`404`、`500`

3. **用户体验**：
   - 用户可以看到完整的、未经处理的标签信息
   - 便于用户识别具体的监控目标和服务
   - 保持与Prometheus查询结果的完全一致性

### 性能优化

- 减少了重复的标签处理步骤，提升渲染性能
- 简化了Prometheus查询，减少服务器端计算负担
- 优化了前端数据处理逻辑，减少不必要的循环和函数调用

## 兼容性说明

- 修复保持了向后兼容性，不影响现有的其他监控指标
- 所有现有的标签清理规则继续有效
- 图表配置和样式保持不变

## 后续建议

1. **监控验证**：建议在生产环境中监控修复效果，确保所有Token相关指标和TCP连接数指标显示正常
2. **性能监控**：关注页面加载性能，确保优化后的处理逻辑没有引入性能问题
3. **用户反馈**：收集用户对修复后图表显示效果的反馈
4. **扩展测试**：可以基于现有测试工具添加更多边界情况的测试用例

## 相关文件

### 修改的文件
- `src/pages/MonitorAlert/index.tsx` - 简化TCP连接数查询
- `src/pages/MonitorAlert/components/AlertChart.tsx` - 优化数据处理流程
- `src/pages/MonitorAlert/components/MultiSeriesChart.tsx` - 优化数据处理流程
- `src/utils/serviceNameExtractor.ts` - 增强服务名提取逻辑
- `src/utils/labelCleaner.ts` - 添加cluster_name支持

### 新增的文件
- `src/utils/testMonitoringFixes.ts` - 监控修复测试工具
- `src/utils/testRawLabels.ts` - 原始标签值显示测试工具
- `MONITORING_FIXES_README.md` - 本修复说明文档

---

**修复完成时间**：2025年7月31日  
**修复人员**：AI Assistant  
**测试状态**：已完成单元测试，待生产环境验证
