# 多标签查询显示问题修复总结

## 问题描述

用户报告消费者Token统计的监控面板存在标签项数量不匹配问题：
- **查询语句**：`sum by (ai_cluster, ai_consumer) (route_upstream_model_consumer_metric_output_token{user_namespace="$namespace"})`
- **Prometheus返回**：5个时间序列
- **图表显示**：图例只有3个标签项，但图表有5条数据线
- **期望结果**：图例应该有5个标签项，与数据线数量完全匹配

## 问题原因分析

### 原始查询结果
```
{ai_cluster="outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local", ai_consumer="consumer"}	1746
{ai_cluster="outbound|80|user-ollama-service-v2|ollama-service-v2.ollama.svc.cluster.local", ai_consumer="consumer"}	755
{ai_cluster="outbound|80|default-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local", ai_consumer="consumer"}	7555
{ai_cluster="outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local", ai_consumer="consumer2"}	3123
{ai_cluster="outbound|80|user-ollama-service-v2|ollama-service-v2.ollama.svc.cluster.local", ai_consumer="consumer2"}	[值]
```

### 问题根源
之前的代码只提取第一个标签（`ai_cluster`）作为系列名称：
```typescript
// 问题代码
const key = keys[0];  // 只取第一个标签
const rawValue = series.metric[key];
seriesName = rawValue;  // 导致多个时间序列使用相同的系列名称
```

这导致：
- 第1个和第4个时间序列都使用相同的`ai_cluster`值作为系列名称
- 第2个和第5个时间序列都使用相同的`ai_cluster`值作为系列名称
- 图例中出现重复的系列名称，被合并显示
- 最终图例只显示3个唯一的标签项，但实际有5条数据线

## 修复方案

### 新的标签处理逻辑
```typescript
// 修复后的代码
if (keys.length === 1) {
  // 单标签情况：直接使用标签值
  const key = keys[0];
  const rawValue = series.metric[key];
  seriesName = rawValue;
} else {
  // 多标签情况：组合所有标签值，用 | 分隔
  const labelParts = keys.map(key => series.metric[key]);
  seriesName = labelParts.join(' | ');
}
```

### 修复后的显示效果
现在每个时间序列都会有唯一的系列名称：
1. `outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local | consumer`
2. `outbound|80|user-ollama-service-v2|ollama-service-v2.ollama.svc.cluster.local | consumer`
3. `outbound|80|default-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local | consumer`
4. `outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local | consumer2`
5. `outbound|80|user-ollama-service-v2|ollama-service-v2.ollama.svc.cluster.local | consumer2`

## 技术实现

### 修改的文件
1. **`src/pages/MonitorAlert/components/AlertChart.tsx`**
   - 更新了系列名称生成逻辑（第207-224行）
   - 支持单标签和多标签的不同处理方式

2. **`src/pages/MonitorAlert/components/MultiSeriesChart.tsx`**
   - 更新了系列名称生成逻辑（第160-177行）
   - 保持与AlertChart相同的处理逻辑

### 核心逻辑改进
- **单标签查询**：直接使用标签的原始值
- **多标签查询**：将所有标签值用` | `连接，确保每个时间序列都有唯一的标识

### 兼容性保证
- 对于现有的单标签查询（如`sum by (ai_route)`），显示效果保持不变
- 对于多标签查询，现在能正确显示所有标签的组合信息
- 不影响其他监控面板的正常显示

## 测试验证

### 专门的测试函数
创建了`testMultiLabelQueries()`函数来专门测试多标签查询：

```javascript
// 在浏览器控制台运行
window.testRawLabels.testMultiLabelQueries();
```

### 测试覆盖
- ✅ 验证5个时间序列生成5个唯一的系列名称
- ✅ 确认没有重复的标签名称
- ✅ 验证标签数量与时间序列数量完全匹配
- ✅ 测试标签组合的正确性

## 预期效果

### 修复前
- 图例显示：3个标签项
- 数据线：5条
- 问题：标签项数量 ≠ 数据线数量

### 修复后
- 图例显示：5个标签项
- 数据线：5条
- 结果：标签项数量 = 数据线数量 ✅

### 用户体验改进
1. **完整性**：每条数据线都有对应的图例标签项
2. **可识别性**：用户可以清楚地区分不同的ai_cluster和ai_consumer组合
3. **一致性**：图例项数量与实际数据完全匹配
4. **透明性**：显示完整的标签信息，不丢失任何维度

## 适用场景

这个修复适用于所有使用多标签聚合的Prometheus查询：
- `sum by (label1, label2)`
- `avg by (label1, label2, label3)`
- `max by (cluster, service)`
- `count by (status, method)`

## 构建验证

- ✅ 项目构建成功
- ✅ 没有编译错误
- ✅ 所有修改已正确集成

## 部署建议

1. **重点测试**：优先测试使用多标签查询的监控面板
2. **用户通知**：告知用户多标签查询现在会显示完整的标签组合
3. **性能监控**：观察多标签显示对页面性能的影响
4. **反馈收集**：收集用户对新标签显示方式的反馈

## 总结

✅ **问题解决**：消费者Token统计面板现在会显示5个图例标签项，与5条数据线完全匹配  
✅ **通用修复**：所有多标签查询都能正确显示标签组合  
✅ **向后兼容**：单标签查询的显示效果保持不变  
✅ **测试完备**：提供了专门的测试工具验证修复效果  
✅ **构建通过**：所有修改都已成功集成并通过构建测试  

现在监控告警页面能够正确处理各种类型的Prometheus查询，确保图例标签项数量与数据线数量完全一致，为用户提供准确、完整的监控信息展示。

---

**修复完成时间**：2025年7月31日  
**问题类型**：多标签查询显示不匹配  
**修复状态**：已完成，构建验证通过  
**影响范围**：所有使用多标签聚合的监控面板
