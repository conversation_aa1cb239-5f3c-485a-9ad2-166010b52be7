/**
 * 服务名称提取工具
 * 用于从完整的 ai_cluster 标签值中提取简洁的服务名称
 */

/**
 * 从 ai_cluster 或 cluster_name 标签值中提取服务名称
 * @param clusterValue ai_cluster 或 cluster_name 标签的完整值
 * @returns 提取出的简洁服务名称
 *
 * 示例：
 * - 输入: "outbound|80|default-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local"
 * - 输出: "ollama-service-v1"
 *
 * - 输入: "outbound|80|user-service|user-service.default.svc.cluster.local"
 * - 输出: "user-service"
 *
 * - 输入: "outbound|8379||redis-service.redis-aigw.svc.cluster.local"
 * - 输出: "redis-service"
 */
export function extractServiceNameFromAiCluster(clusterValue: string): string {
  if (!clusterValue || typeof clusterValue !== 'string') {
    console.log('❌ extractServiceNameFromAiCluster: 无效输入', clusterValue);
    return clusterValue || '';
  }

  console.log('🔍 extractServiceNameFromAiCluster: 处理 cluster 值', clusterValue);

  // 处理格式：outbound|80|default-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local
  if (clusterValue.includes('|')) {
    const parts = clusterValue.split('|');
    if (parts.length >= 4) {
      // 取第4部分（索引3），然后提取服务名
      const fullServiceName = parts[3];

      // 从完整服务名中提取服务名称部分
      // 格式：ollama-service-v1.ollama.svc.cluster.local -> ollama-service-v1
      if (fullServiceName.includes('.')) {
        const serviceName = fullServiceName.split('.')[0];
        console.log(`✅ extractServiceNameFromAiCluster: 从管道格式提取 "${clusterValue}" -> "${serviceName}"`);
        return serviceName;
      }

      console.log(`✅ extractServiceNameFromAiCluster: 从管道格式提取 "${clusterValue}" -> "${fullServiceName}"`);
      return fullServiceName;
    }

    // 如果分割后长度不够，尝试取第3部分（索引2）
    if (parts.length >= 3) {
      const serviceName = parts[2];
      // 如果第3部分为空，尝试从第4部分提取
      if (!serviceName && parts.length >= 4) {
        const fullServiceName = parts[3];
        if (fullServiceName.includes('.')) {
          const extractedName = fullServiceName.split('.')[0];
          console.log(`✅ extractServiceNameFromAiCluster: 从管道格式提取（第4部分） "${clusterValue}" -> "${extractedName}"`);
          return extractedName;
        }
        console.log(`✅ extractServiceNameFromAiCluster: 从管道格式提取（第4部分） "${clusterValue}" -> "${fullServiceName}"`);
        return fullServiceName;
      }

      // 移除可能的前缀（如 default-）
      const cleanedName = serviceName.replace(/^default-/, '').replace(/^user-/, '');
      console.log(`✅ extractServiceNameFromAiCluster: 从管道格式提取（第3部分） "${clusterValue}" -> "${cleanedName}"`);
      return cleanedName || serviceName;
    }
  }

  // 处理直接的服务名格式：service-name.namespace.svc.cluster.local
  if (clusterValue.includes('.svc.cluster.local')) {
    const serviceName = clusterValue.split('.')[0];
    console.log(`✅ extractServiceNameFromAiCluster: 从域名格式提取 "${clusterValue}" -> "${serviceName}"`);
    return serviceName;
  }

  // 处理简单的服务名格式，移除可能的前缀
  let cleanedName = clusterValue;

  // 移除常见前缀
  if (cleanedName.startsWith('default-')) {
    cleanedName = cleanedName.substring(8); // 移除 "default-"
  }
  if (cleanedName.startsWith('user-')) {
    cleanedName = cleanedName.substring(5); // 移除 "user-"
  }

  if (cleanedName !== clusterValue) {
    console.log(`✅ extractServiceNameFromAiCluster: 移除前缀 "${clusterValue}" -> "${cleanedName}"`);
  } else {
    console.log(`⚪ extractServiceNameFromAiCluster: 无需处理 "${clusterValue}"`);
  }

  return cleanedName;
}

/**
 * 批量提取服务名称
 * @param aiClusterValues ai_cluster 标签值数组
 * @returns 提取出的服务名称数组
 */
export function extractServiceNamesFromAiClusters(aiClusterValues: string[]): string[] {
  if (!Array.isArray(aiClusterValues)) {
    return aiClusterValues;
  }

  return aiClusterValues.map(value => extractServiceNameFromAiCluster(value));
}

/**
 * 为图表数据处理 ai_cluster 标签
 * @param seriesData 系列数据数组
 * @returns 处理后的系列数据数组
 */
export function processAiClusterLabelsInSeriesData(
  seriesData: Array<{ name: string; originalName?: string; [key: string]: any }>
): Array<{ name: string; originalName: string; [key: string]: any }> {
  if (!Array.isArray(seriesData)) {
    return seriesData;
  }

  console.log('🚀 processAiClusterLabelsInSeriesData: 开始处理', {
    seriesCount: seriesData.length
  });

  return seriesData.map((series, index) => {
    console.log(`🔄 处理第 ${index + 1} 个系列: "${series.name}"`);

    // 检查是否包含 ai_cluster 相关的标签
    if (series.name && typeof series.name === 'string') {
      // 如果名称看起来像是 ai_cluster 值，进行提取
      if (series.name.includes('|') || series.name.includes('.svc.cluster.local') || 
          series.name.startsWith('outbound') || series.name.includes('default-')) {
        
        const extractedName = extractServiceNameFromAiCluster(series.name);
        
        if (extractedName !== series.name) {
          console.log(`✅ ai_cluster 标签已处理: "${series.name}" -> "${extractedName}"`);
          
          return {
            ...series,
            name: extractedName,
            originalName: series.originalName || series.name
          };
        }
      }
    }

    console.log(`⚪ 系列无需处理: "${series.name}"`);
    
    return {
      ...series,
      originalName: series.originalName || series.name
    };
  });
}

/**
 * 检查标签是否是 ai_cluster 或 cluster_name 格式
 * @param labelName 标签名称
 * @returns 是否是 cluster 格式
 */
export function isAiClusterLabel(labelName: string): boolean {
  if (!labelName || typeof labelName !== 'string') {
    return false;
  }

  return labelName.includes('|') ||
         labelName.includes('.svc.cluster.local') ||
         labelName.startsWith('outbound') ||
         (labelName.includes('default-') && labelName.length > 20) ||
         (labelName.includes('redis-service') && labelName.includes('redis-aigw'));
}

/**
 * 获取 ai_cluster 处理统计信息
 * @param originalNames 原始标签名称数组
 * @returns 处理统计信息
 */
export function getAiClusterProcessingStats(originalNames: string[]): {
  total: number;
  processed: number;
  unchanged: number;
  processingRate: number;
} {
  if (!Array.isArray(originalNames)) {
    return { total: 0, processed: 0, unchanged: 0, processingRate: 0 };
  }

  const total = originalNames.length;
  const processed = originalNames.filter(name => isAiClusterLabel(name)).length;
  const unchanged = total - processed;
  const processingRate = total > 0 ? (processed / total) * 100 : 0;

  return {
    total,
    processed,
    unchanged,
    processingRate: Math.round(processingRate * 100) / 100
  };
}