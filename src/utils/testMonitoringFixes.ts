/**
 * 监控告警页面修复测试工具
 * 用于验证Token指标标签项数量匹配和服务维度TCP连接数显示修复
 */

import { extractServiceNameFromAiCluster, isAiClusterLabel } from './serviceNameExtractor';
import { cleanLabelName } from './labelCleaner';

/**
 * 测试服务名提取功能
 */
export function testServiceNameExtraction() {
  console.log('🧪 开始测试服务名提取功能...');
  
  const testCases = [
    // TCP连接数相关的cluster_name格式
    'outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local',
    'outbound|8379||redis-service.redis-aigw.svc.cluster.local',
    'outbound|80|default-user-service|user-service.default.svc.cluster.local',
    
    // ai_cluster格式
    'outbound|80|default-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local',
    'outbound|80|user-service|user-service.default.svc.cluster.local',
    
    // 简单格式
    'ollama-service-v1.ollama.svc.cluster.local',
    'redis-service.redis-aigw.svc.cluster.local',
    'user-service',
    'default-ollama-service'
  ];
  
  testCases.forEach((testCase, index) => {
    const result = extractServiceNameFromAiCluster(testCase);
    console.log(`测试 ${index + 1}: "${testCase}" -> "${result}"`);
  });
  
  console.log('✅ 服务名提取功能测试完成');
}

/**
 * 测试标签清理功能
 */
export function testLabelCleaning() {
  console.log('🧪 开始测试标签清理功能...');
  
  const testCases = [
    'cluster_name:outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local',
    'ai_cluster:outbound|8379||redis-service.redis-aigw.svc.cluster.local',
    'destination_service_name:user-service',
    'response_code:200',
    'ai_route:chat-completion',
    'ai_consumer:user-123'
  ];
  
  testCases.forEach((testCase, index) => {
    const result = cleanLabelName(testCase);
    console.log(`测试 ${index + 1}: "${testCase}" -> "${result}"`);
  });
  
  console.log('✅ 标签清理功能测试完成');
}

/**
 * 测试cluster标签识别功能
 */
export function testClusterLabelIdentification() {
  console.log('🧪 开始测试cluster标签识别功能...');
  
  const testCases = [
    { label: 'outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local', expected: true },
    { label: 'outbound|8379||redis-service.redis-aigw.svc.cluster.local', expected: true },
    { label: 'ollama-service-v1.ollama.svc.cluster.local', expected: true },
    { label: 'redis-service.redis-aigw.svc.cluster.local', expected: true },
    { label: 'user-service', expected: false },
    { label: 'response_code', expected: false },
    { label: 'ai_route', expected: false }
  ];
  
  testCases.forEach((testCase, index) => {
    const result = isAiClusterLabel(testCase.label);
    const status = result === testCase.expected ? '✅' : '❌';
    console.log(`测试 ${index + 1}: "${testCase.label}" -> ${result} ${status}`);
  });
  
  console.log('✅ cluster标签识别功能测试完成');
}

/**
 * 模拟Token指标数据处理测试
 */
export function testTokenMetricsProcessing() {
  console.log('🧪 开始测试Token指标数据处理...');
  
  // 模拟Prometheus查询返回的数据
  const mockPrometheusData = [
    {
      metric: { ai_route: 'chat-completion', ai_cluster: 'outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local' },
      values: [[1640995200, '100'], [1640995260, '120'], [1640995320, '110']]
    },
    {
      metric: { ai_route: 'embeddings', ai_cluster: 'outbound|80|user-embedding-service|embedding-service.default.svc.cluster.local' },
      values: [[1640995200, '50'], [1640995260, '60'], [1640995320, '55']]
    },
    {
      metric: { ai_route: 'chat-completion', ai_consumer: 'user-123' },
      values: [[1640995200, '80'], [1640995260, '90'], [1640995320, '85']]
    }
  ];
  
  // 模拟数据处理逻辑
  const processedSeries = mockPrometheusData.map((series, index) => {
    let seriesName = `Series ${index + 1}`;
    
    if (series.metric) {
      if (series.metric.ai_route && series.metric.ai_cluster) {
        const cleanServiceName = extractServiceNameFromAiCluster(series.metric.ai_cluster);
        seriesName = `${series.metric.ai_route} - ${cleanServiceName}`;
      } else if (series.metric.ai_route && series.metric.ai_consumer) {
        seriesName = `${series.metric.ai_route} - ${series.metric.ai_consumer}`;
      } else if (series.metric.ai_route) {
        seriesName = series.metric.ai_route;
      }
    }
    
    const finalCleanedName = cleanLabelName(seriesName);
    
    return {
      name: finalCleanedName,
      originalName: seriesName,
      data: series.values.map(([timestamp, value]) => [timestamp * 1000, parseFloat(value)])
    };
  });
  
  console.log('处理后的系列数据:');
  processedSeries.forEach((series, index) => {
    console.log(`系列 ${index + 1}: "${series.name}" (原始: "${series.originalName}")`);
  });
  
  console.log(`✅ Token指标数据处理测试完成，共处理 ${processedSeries.length} 个系列`);
  
  return processedSeries;
}

/**
 * 模拟TCP连接数数据处理测试
 */
export function testTcpConnectionProcessing() {
  console.log('🧪 开始测试TCP连接数数据处理...');
  
  // 模拟Prometheus查询返回的数据
  const mockPrometheusData = [
    {
      metric: { cluster_name: 'outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local' },
      values: [[1640995200, '5'], [1640995260, '7'], [1640995320, '6']]
    },
    {
      metric: { cluster_name: 'outbound|8379||redis-service.redis-aigw.svc.cluster.local' },
      values: [[1640995200, '2'], [1640995260, '3'], [1640995320, '2']]
    },
    {
      metric: { cluster_name: 'outbound|80|default-user-service|user-service.default.svc.cluster.local' },
      values: [[1640995200, '4'], [1640995260, '5'], [1640995320, '4']]
    }
  ];
  
  // 模拟数据处理逻辑
  const processedSeries = mockPrometheusData.map((series, index) => {
    let seriesName = `Series ${index + 1}`;
    
    if (series.metric && series.metric.cluster_name) {
      seriesName = extractServiceNameFromAiCluster(series.metric.cluster_name);
    }
    
    const finalCleanedName = cleanLabelName(seriesName);
    
    return {
      name: finalCleanedName,
      originalName: seriesName,
      data: series.values.map(([timestamp, value]) => [timestamp * 1000, parseFloat(value)])
    };
  });
  
  console.log('处理后的系列数据:');
  processedSeries.forEach((series, index) => {
    console.log(`系列 ${index + 1}: "${series.name}" (原始: "${series.originalName}")`);
  });
  
  console.log(`✅ TCP连接数数据处理测试完成，共处理 ${processedSeries.length} 个系列`);
  
  return processedSeries;
}

/**
 * 运行所有测试
 */
export function runAllTests() {
  console.log('🚀 开始运行监控告警页面修复测试...');
  console.log('='.repeat(50));
  
  testServiceNameExtraction();
  console.log('-'.repeat(30));
  
  testLabelCleaning();
  console.log('-'.repeat(30));
  
  testClusterLabelIdentification();
  console.log('-'.repeat(30));
  
  const tokenResults = testTokenMetricsProcessing();
  console.log('-'.repeat(30));
  
  const tcpResults = testTcpConnectionProcessing();
  console.log('-'.repeat(30));
  
  console.log('📊 测试总结:');
  console.log(`Token指标处理: ${tokenResults.length} 个系列`);
  console.log(`TCP连接数处理: ${tcpResults.length} 个系列`);
  console.log('✅ 所有测试完成！');
  
  return {
    tokenResults,
    tcpResults
  };
}

// 将测试函数注册到全局对象，方便在浏览器控制台中调用
if (typeof window !== 'undefined') {
  (window as any).testMonitoringFixes = {
    testServiceNameExtraction,
    testLabelCleaning,
    testClusterLabelIdentification,
    testTokenMetricsProcessing,
    testTcpConnectionProcessing,
    runAllTests
  };
  
  console.log('🔧 监控修复测试工具已注册到 window.testMonitoringFixes');
}
