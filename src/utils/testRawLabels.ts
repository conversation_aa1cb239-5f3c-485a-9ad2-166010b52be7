/**
 * 原始标签值显示测试工具
 * 用于验证监控告警页面直接使用Prometheus原始标签值作为图例显示
 */

/**
 * 模拟Prometheus查询返回的原始数据
 */
export const mockPrometheusResponses = {
  // Token相关指标 - sum by (ai_route)
  tokenByRoute: [
    {
      metric: { ai_route: 'chat-completion' },
      values: [[1640995200, '100'], [1640995260, '120'], [1640995320, '110']]
    },
    {
      metric: { ai_route: 'embeddings' },
      values: [[1640995200, '50'], [1640995260, '60'], [1640995320, '55']]
    },
    {
      metric: { ai_route: 'text-generation' },
      values: [[1640995200, '80'], [1640995260, '90'], [1640995320, '85']]
    }
  ],

  // Token相关指标 - sum by (ai_cluster)
  tokenByCluster: [
    {
      metric: { ai_cluster: 'outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local' },
      values: [[1640995200, '150'], [1640995260, '180'], [1640995320, '165']]
    },
    {
      metric: { ai_cluster: 'outbound|80|user-embedding-service|embedding-service.default.svc.cluster.local' },
      values: [[1640995200, '75'], [1640995260, '85'], [1640995320, '80']]
    },
    {
      metric: { ai_cluster: 'outbound|80|default-text-service|text-service.ai.svc.cluster.local' },
      values: [[1640995200, '60'], [1640995260, '70'], [1640995320, '65']]
    }
  ],

  // TCP连接数 - sum by (cluster_name)
  tcpByClusterName: [
    {
      metric: { cluster_name: 'outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local' },
      values: [[1640995200, '5'], [1640995260, '7'], [1640995320, '6']]
    },
    {
      metric: { cluster_name: 'outbound|8379||redis-service.redis-aigw.svc.cluster.local' },
      values: [[1640995200, '2'], [1640995260, '3'], [1640995320, '2']]
    },
    {
      metric: { cluster_name: 'outbound|80|default-user-service|user-service.default.svc.cluster.local' },
      values: [[1640995200, '4'], [1640995260, '5'], [1640995320, '4']]
    }
  ],

  // 响应码统计 - sum by (response_code)
  responseByCode: [
    {
      metric: { response_code: '200' },
      values: [[1640995200, '1000'], [1640995260, '1200'], [1640995320, '1100']]
    },
    {
      metric: { response_code: '404' },
      values: [[1640995200, '50'], [1640995260, '60'], [1640995320, '55']]
    },
    {
      metric: { response_code: '500' },
      values: [[1640995200, '10'], [1640995260, '15'], [1640995320, '12']]
    }
  ],

  // 多标签查询 - sum by (ai_cluster, ai_consumer)
  multiLabelByClusterAndConsumer: [
    {
      metric: {
        ai_cluster: 'outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local',
        ai_consumer: 'consumer'
      },
      values: [[1640995200, '1746'], [1640995260, '1800'], [1640995320, '1750']]
    },
    {
      metric: {
        ai_cluster: 'outbound|80|user-ollama-service-v2|ollama-service-v2.ollama.svc.cluster.local',
        ai_consumer: 'consumer'
      },
      values: [[1640995200, '755'], [1640995260, '800'], [1640995320, '760']]
    },
    {
      metric: {
        ai_cluster: 'outbound|80|default-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local',
        ai_consumer: 'consumer'
      },
      values: [[1640995200, '7555'], [1640995260, '7600'], [1640995320, '7580']]
    },
    {
      metric: {
        ai_cluster: 'outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local',
        ai_consumer: 'consumer2'
      },
      values: [[1640995200, '3123'], [1640995260, '3200'], [1640995320, '3150']]
    },
    {
      metric: {
        ai_cluster: 'outbound|80|user-ollama-service-v2|ollama-service-v2.ollama.svc.cluster.local',
        ai_consumer: 'consumer2'
      },
      values: [[1640995200, '1500'], [1640995260, '1550'], [1640995320, '1520']]
    }
  ]
};

/**
 * 测试原始标签值提取逻辑
 */
export function testRawLabelExtraction() {
  console.log('🧪 开始测试原始标签值提取逻辑...');
  
  const testCases = [
    {
      name: 'Token指标 - ai_route',
      data: mockPrometheusResponses.tokenByRoute,
      expectedLabels: ['chat-completion', 'embeddings', 'text-generation']
    },
    {
      name: 'Token指标 - ai_cluster',
      data: mockPrometheusResponses.tokenByCluster,
      expectedLabels: [
        'outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local',
        'outbound|80|user-embedding-service|embedding-service.default.svc.cluster.local',
        'outbound|80|default-text-service|text-service.ai.svc.cluster.local'
      ]
    },
    {
      name: 'TCP连接数 - cluster_name',
      data: mockPrometheusResponses.tcpByClusterName,
      expectedLabels: [
        'outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local',
        'outbound|8379||redis-service.redis-aigw.svc.cluster.local',
        'outbound|80|default-user-service|user-service.default.svc.cluster.local'
      ]
    },
    {
      name: '响应码统计 - response_code',
      data: mockPrometheusResponses.responseByCode,
      expectedLabels: ['200', '404', '500']
    },
    {
      name: '多标签查询 - ai_cluster + ai_consumer',
      data: mockPrometheusResponses.multiLabelByClusterAndConsumer,
      expectedLabels: [
        'outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local | consumer',
        'outbound|80|user-ollama-service-v2|ollama-service-v2.ollama.svc.cluster.local | consumer',
        'outbound|80|default-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local | consumer',
        'outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local | consumer2',
        'outbound|80|user-ollama-service-v2|ollama-service-v2.ollama.svc.cluster.local | consumer2'
      ]
    }
  ];

  testCases.forEach((testCase, caseIndex) => {
    console.log(`\n测试用例 ${caseIndex + 1}: ${testCase.name}`);
    
    const extractedLabels = testCase.data.map((series, index) => {
      // 模拟新的标签提取逻辑：支持单标签和多标签
      let seriesName = `Series ${index + 1}`;
      if (series.metric) {
        const keys = Object.keys(series.metric);
        if (keys.length > 0) {
          if (keys.length === 1) {
            // 单标签情况：直接使用标签值
            const key = keys[0];
            const rawValue = series.metric[key];
            seriesName = rawValue;
          } else {
            // 多标签情况：组合所有标签值，用 | 分隔
            const labelParts = keys.map(key => series.metric[key]);
            seriesName = labelParts.join(' | ');
          }
        }
      }
      return seriesName;
    });

    console.log('提取的标签值:', extractedLabels);
    console.log('期望的标签值:', testCase.expectedLabels);
    
    // 验证结果
    const isMatch = JSON.stringify(extractedLabels) === JSON.stringify(testCase.expectedLabels);
    console.log(`结果验证: ${isMatch ? '✅ 通过' : '❌ 失败'}`);
    
    if (!isMatch) {
      console.log('差异详情:');
      extractedLabels.forEach((extracted, i) => {
        const expected = testCase.expectedLabels[i];
        if (extracted !== expected) {
          console.log(`  索引 ${i}: 提取值 "${extracted}" != 期望值 "${expected}"`);
        }
      });
    }
  });
  
  console.log('\n✅ 原始标签值提取逻辑测试完成');
}

/**
 * 测试标签数量一致性
 */
export function testLabelCountConsistency() {
  console.log('🧪 开始测试标签数量一致性...');
  
  Object.entries(mockPrometheusResponses).forEach(([key, data]) => {
    const prometheusSeriesCount = data.length;
    const extractedLabelsCount = data.filter(series => {
      if (series.metric) {
        const keys = Object.keys(series.metric);
        return keys.length > 0;
      }
      return false;
    }).length;
    
    console.log(`${key}:`, {
      Prometheus时间序列数量: prometheusSeriesCount,
      可提取标签的系列数量: extractedLabelsCount,
      一致性: prometheusSeriesCount === extractedLabelsCount ? '✅' : '❌'
    });
  });
  
  console.log('✅ 标签数量一致性测试完成');
}

/**
 * 测试完整的数据处理流程
 */
export function testCompleteDataProcessing() {
  console.log('🧪 开始测试完整的数据处理流程...');
  
  // 模拟AlertChart和MultiSeriesChart中的数据处理逻辑
  const processSeriesData = (resultArray: any[], title: string) => {
    const colors = [
      '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#fa541c',
      '#13c2c2', '#eb2f96', '#fa8c16', '#a0d911', '#096dd9', '#36cfc9'
    ];
    
    const processedSeries = resultArray.map((series: any, index: number) => {
      const data = series.values?.map(([timestamp, value]: [number, string]) => [
        timestamp * 1000,
        parseFloat(value) || 0
      ]) || [];

      // 直接使用Prometheus metric对象中的原始标签值作为系列名称
      let seriesName = `Series ${index + 1}`;
      if (series.metric) {
        const keys = Object.keys(series.metric);
        if (keys.length > 0) {
          if (keys.length === 1) {
            // 单标签情况：直接使用标签值
            const key = keys[0];
            const rawValue = series.metric[key];
            seriesName = rawValue;
          } else {
            // 多标签情况：组合所有标签值，用 | 分隔
            const labelParts = keys.map(key => series.metric[key]);
            seriesName = labelParts.join(' | ');
          }
        }
      }

      return {
        name: seriesName,
        originalName: seriesName,
        data,
        color: colors[index % colors.length]
      };
    });

    console.log(`${title} 处理结果:`, {
      原始系列数量: resultArray.length,
      处理后系列数量: processedSeries.length,
      原始标签值: processedSeries.map(s => s.name)
    });

    return processedSeries;
  };

  // 测试各种数据类型
  console.log('\n--- Token指标 (ai_route) ---');
  processSeriesData(mockPrometheusResponses.tokenByRoute, 'Token by Route');

  console.log('\n--- Token指标 (ai_cluster) ---');
  processSeriesData(mockPrometheusResponses.tokenByCluster, 'Token by Cluster');

  console.log('\n--- TCP连接数 (cluster_name) ---');
  processSeriesData(mockPrometheusResponses.tcpByClusterName, 'TCP by Cluster Name');

  console.log('\n--- 响应码统计 (response_code) ---');
  processSeriesData(mockPrometheusResponses.responseByCode, 'Response by Code');

  console.log('\n--- 多标签查询 (ai_cluster + ai_consumer) ---');
  processSeriesData(mockPrometheusResponses.multiLabelByClusterAndConsumer, 'Multi-Label by Cluster and Consumer');

  console.log('\n✅ 完整数据处理流程测试完成');
}

/**
 * 验证原始标签值的完整性
 */
export function testLabelIntegrity() {
  console.log('🧪 开始测试原始标签值的完整性...');
  
  const testCases = [
    {
      name: '长cluster_name标签',
      value: 'outbound|80|user-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local',
      shouldPreserve: true
    },
    {
      name: '带空字段的cluster_name',
      value: 'outbound|8379||redis-service.redis-aigw.svc.cluster.local',
      shouldPreserve: true
    },
    {
      name: '简单的ai_route',
      value: 'chat-completion',
      shouldPreserve: true
    },
    {
      name: '数字响应码',
      value: '200',
      shouldPreserve: true
    },
    {
      name: '特殊字符',
      value: 'service-name_v1.2.3',
      shouldPreserve: true
    }
  ];

  testCases.forEach((testCase, index) => {
    // 模拟新的处理逻辑：直接返回原始值
    const processedValue = testCase.value; // 不进行任何处理
    
    const isIntact = processedValue === testCase.value;
    console.log(`测试 ${index + 1}: "${testCase.name}"`);
    console.log(`  原始值: "${testCase.value}"`);
    console.log(`  处理后: "${processedValue}"`);
    console.log(`  完整性: ${isIntact ? '✅ 保持完整' : '❌ 被修改'}`);
  });

  console.log('\n✅ 原始标签值完整性测试完成');
}

/**
 * 专门测试多标签查询的标签显示
 */
export function testMultiLabelQueries() {
  console.log('🧪 开始测试多标签查询的标签显示...');

  const multiLabelData = mockPrometheusResponses.multiLabelByClusterAndConsumer;

  console.log('原始Prometheus查询结果:');
  multiLabelData.forEach((series, index) => {
    console.log(`系列 ${index + 1}:`, series.metric);
  });

  console.log('\n处理后的标签显示:');
  const processedLabels = multiLabelData.map((series, index) => {
    let seriesName = `Series ${index + 1}`;
    if (series.metric) {
      const keys = Object.keys(series.metric);
      if (keys.length > 0) {
        if (keys.length === 1) {
          // 单标签情况：直接使用标签值
          const key = keys[0];
          const rawValue = series.metric[key];
          seriesName = rawValue;
        } else {
          // 多标签情况：组合所有标签值，用 | 分隔
          const labelParts = keys.map(key => series.metric[key]);
          seriesName = labelParts.join(' | ');
        }
      }
    }
    console.log(`系列 ${index + 1}: "${seriesName}"`);
    return seriesName;
  });

  console.log('\n验证结果:');
  console.log(`Prometheus时间序列数量: ${multiLabelData.length}`);
  console.log(`处理后标签数量: ${processedLabels.length}`);
  console.log(`数量一致性: ${multiLabelData.length === processedLabels.length ? '✅' : '❌'}`);

  // 检查是否有重复的标签名称
  const uniqueLabels = [...new Set(processedLabels)];
  console.log(`唯一标签数量: ${uniqueLabels.length}`);
  console.log(`标签唯一性: ${uniqueLabels.length === processedLabels.length ? '✅' : '❌'}`);

  if (uniqueLabels.length !== processedLabels.length) {
    console.log('⚠️ 发现重复标签，这会导致图例项数量少于数据线条数量！');
    const duplicates = processedLabels.filter((label, index) => processedLabels.indexOf(label) !== index);
    console.log('重复的标签:', [...new Set(duplicates)]);
  }

  console.log('\n✅ 多标签查询测试完成');
}

/**
 * 运行所有测试
 */
export function runAllRawLabelTests() {
  console.log('🚀 开始运行原始标签值显示测试...');
  console.log('='.repeat(60));
  
  testRawLabelExtraction();
  console.log('-'.repeat(40));
  
  testLabelCountConsistency();
  console.log('-'.repeat(40));
  
  testCompleteDataProcessing();
  console.log('-'.repeat(40));
  
  testLabelIntegrity();
  console.log('-'.repeat(40));

  testMultiLabelQueries();
  console.log('-'.repeat(40));
  
  console.log('📊 测试总结:');
  console.log('✅ 所有原始标签值显示测试完成！');
  console.log('🎯 图例将直接显示Prometheus查询中sum by()或group by()括号内的原始标签值');
  console.log('🔒 不进行任何标签清理、服务名提取或前缀移除处理');
  console.log('📈 确保图例标签项数量与Prometheus返回的时间序列数量完全一致');
}

// 将测试函数注册到全局对象，方便在浏览器控制台中调用
if (typeof window !== 'undefined') {
  (window as any).testRawLabels = {
    testRawLabelExtraction,
    testLabelCountConsistency,
    testCompleteDataProcessing,
    testLabelIntegrity,
    testMultiLabelQueries,
    runAllRawLabelTests,
    mockPrometheusResponses
  };
  
  console.log('🔧 原始标签值测试工具已注册到 window.testRawLabels');
}
