import React from 'react';
import { Tooltip } from 'acud';
import { OutlinedQuestionCircle } from 'acud-icon';
import styles from './index.module.less';

interface HelpTooltipProps {
  /**
   * 提示内容
   */
  content: string;
  /**
   * 自定义样式类名
   */
  className?: string;
}

/**
 * 帮助提示组件
 * 用于在监控面板标题旁显示帮助图标和详细说明
 */
const HelpTooltip: React.FC<HelpTooltipProps> = ({
  content,
  className
}) => {
  return (
    <Tooltip
      title={content}
      trigger="hover"
      placement="topLeft"
      overlayClassName={styles.helpTooltip}
    >
      <OutlinedQuestionCircle
        className={`${styles.helpIcon} ${className || ''}`}
      />
    </Tooltip>
  );
};

export default HelpTooltip;
