import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Button, Modal } from 'acud';
import { OutlinedArrowsAlt } from 'acud-icon';
import { useRequest } from 'ahooks';
import * as echarts from 'echarts/core';

import HelpTooltip from '@/components/HelpTooltip';

import { queryMetrics } from '@/apis/monitor';
import { formatXAxisTime, getXAxisTickConfig, formatTooltipTime, validateAndCleanData, validateAndCleanMultiSeriesData } from '@/utils/timeFormatter';
import { formatValueWithUnit, formatYAxisLabel } from '@/utils/numberFormatter';
import { getOptimizedLegendConfig, getOptimizedGridConfig, createDescriptiveFormatter, LEGEND_DESCRIPTIONS, getOriginalDataMap } from '@/utils/chartLegendConfig';
// 移除标签处理相关导入，直接使用原始标签值
// import { cleanLabelName } from '@/utils/labelCleaner';
import { addSimpleLegendTooltip, createCustomLegendTooltip } from '@/utils/simpleLegendTooltip';
// import { extractServiceNameFromAiCluster, isAiClusterLabel } from '@/utils/serviceNameExtractor';
import '@/utils/echartsInit'; // 确保ECharts组件已注册
import styles from './AlertChart.module.less';

interface QueryConfig {
  name: string; // 查询名称，用于图例显示
  query: string; // 查询语句
  color?: string; // 可选的颜色配置
}

interface AlertChartProps {
  title: string;
  unit: string;
  description?: string; // 添加描述字段
  query?: string; // 单查询语句（向后兼容）
  queries?: QueryConfig[]; // 多查询语句配置
  instanceId: string;
  timeRange: {
    start: number;
    end: number;
  };
  refreshTrigger?: any; // 用于触发刷新的依赖
  region: string; // 添加region参数
  metricType?: 'basic' | 'business'; // 添加metricType参数，默认为business
  allowQueries?: boolean; // 添加是否允许查询参数
}

const AlertChart: React.FC<AlertChartProps> = ({
  title,
  unit,
  description,
  query,
  queries,
  instanceId,
  timeRange,
  refreshTrigger,
  region,
  metricType = 'business', // 默认为business类型
  allowQueries = true // 默认为true，保持向后兼容
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const modalChartRef = useRef<HTMLDivElement>(null);
  const modalChartInstance = useRef<echarts.ECharts | null>(null);
  const [chartData, setChartData] = useState<any[]>([]);
  const [seriesData, setSeriesData] = useState<any[]>([]);

  // 图表模式状态管理 - 从全局状态获取
  const [chartMode, setChartMode] = useState<'line' | 'area'>(() => {
    const globalSaved = localStorage.getItem('global-chart-mode');
    const panelSaved = localStorage.getItem(`chart-mode-${title}`);
    return (panelSaved as 'line' | 'area') || (globalSaved as 'line' | 'area') || 'area';
  });

  // 监听全局模式变化
  useEffect(() => {
    const handleStorageChange = () => {
      const globalMode = localStorage.getItem('global-chart-mode') as 'line' | 'area';
      const panelMode = localStorage.getItem(`chart-mode-${title}`) as 'line' | 'area';
      const newMode = panelMode || globalMode || 'area';
      setChartMode(newMode);
    };

    window.addEventListener('storage', handleStorageChange);
    // 也监听自定义事件，用于同一页面内的状态同步
    window.addEventListener('chartModeChange', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('chartModeChange', handleStorageChange);
    };
  }, [title]);

 // 多系列数据

  console.log(`AlertChart ${title} 渲染`, {
    instanceId,
    query,
    timeRange,
    refreshTrigger
  });

  // 动态step配置函数
  const getDynamicStep = (timeRange: { start: number; end: number }): number => {
    const durationMs = timeRange.end - timeRange.start;
    const durationHours = durationMs / (1000 * 60 * 60);

    // 根据时间范围动态设置step值
    if (durationHours <= 1) {
      return 60; // 1分钟
    } else if (durationHours <= 6) {
      return 300; // 5分钟
    } else if (durationHours <= 24) {
      return 900; // 15分钟
    } else if (durationHours <= 168) { // 7天
      return 3600; // 1小时
    } else {
      return 21600; // 6小时
    }
  };

  // 查询监控数据
  const { run: fetchData, loading } = useRequest(
    () => {
      // 确定要执行的查询列表
      const queryList = queries || (query ? [{ name: title, query }] : []);

      if (!instanceId || queryList.length === 0) {
        console.log(`${title} 跳过数据查询: instanceId=${instanceId}, queryList=${queryList.length}`);
        return Promise.resolve(null);
      }

      const step = getDynamicStep(timeRange); // 使用动态step配置

      console.log(`${title} 开始查询数据:`, {
        instanceId,
        queryList: queryList.map(q => ({ name: q.name, query: q.query })),
        start: Math.floor(timeRange.start / 1000),
        end: Math.floor(timeRange.end / 1000),
        step,
        timeRangeDuration: `${((timeRange.end - timeRange.start) / (1000 * 60 * 60)).toFixed(1)}h`
      });

      // 并行执行多个查询
      const promises = queryList.map(queryConfig =>
        queryMetrics({
          instanceId,
          metricType, // 使用传入的metricType参数
          query: queryConfig.query,
          start: Math.floor(timeRange.start / 1000).toString(),
          end: Math.floor(timeRange.end / 1000).toString(),
          step: step.toString()
        }, region).then(res => ({ ...res, queryName: queryConfig.name, queryConfig }))
      );

      return Promise.all(promises);
    },
    {
      manual: true,
      onSuccess: (res: any) => {
        console.log(`${title} 查询数据成功:`, res);

        if (Array.isArray(res)) {
          // 多查询响应处理 - 支持每个查询返回多个时间序列
          const allSeries: any[] = [];

          res.forEach((queryRes) => {
            if (queryRes?.result?.status === 'success' && queryRes?.result?.data?.result) {
              const resultArray = queryRes.result.data.result;

              // 处理每个查询的所有时间序列
              resultArray.forEach((series: any, seriesIndex: number) => {
                const data = series.values?.map(([timestamp, value]: [number, string]) => [
                  timestamp * 1000, // 转换为毫秒
                  parseFloat(value) || 0
                ]) || [];

                // 构建系列名称：根据是否有标签决定显示格式
                let serviceName = '';
                let hasLabels = false;

                if (series.metric) {
                  const keys = Object.keys(series.metric);
                  if (keys.length > 0) {
                    hasLabels = true;
                    if (keys.length === 1) {
                      // 单标签情况：直接使用标签值
                      const key = keys[0];
                      const rawValue = series.metric[key];
                      serviceName = rawValue;
                    } else {
                      // 多标签情况：组合所有标签值，用 | 分隔
                      const labelParts = keys.map(key => series.metric[key]);
                      serviceName = labelParts.join(' | ');
                    }
                  }
                }

                // 如果没有标签（聚合查询），只显示查询名称；如果有标签，显示"标签值 查询名称"
                const seriesName = hasLabels ? `${serviceName} ${queryRes.queryName}` : queryRes.queryName;

                allSeries.push({
                  name: seriesName,
                  originalName: seriesName,
                  data,
                  color: queryRes.queryConfig?.color,
                  queryName: queryRes.queryName,
                  serviceName: serviceName
                });
              });
            }
          });

          // 验证和清理数据，保持原始时间戳
          const cleanedSeriesData = validateAndCleanMultiSeriesData(allSeries, timeRange);

          console.log(`${title} 多查询数据处理完成:`, {
            原始查询数量: res.length,
            总时间序列数量: allSeries.length,
            处理后系列数量: cleanedSeriesData.length,
            系列名称: cleanedSeriesData.map(s => s.name)
          });

          setSeriesData(cleanedSeriesData);
          setChartData([]); // 清空单系列数据
          console.log(`${title} 最终多系列数据:`, cleanedSeriesData);
        } else {
          // 单查询响应处理
          if (res?.result?.status === 'success' && res?.result?.data?.result) {
            const resultArray = res.result.data.result;
            console.log(`${title} 查询结果数组长度:`, resultArray.length);
            console.log(`${title} 查询结果详情:`, resultArray);

            // 检查是否有多个时间序列（如带有by子句的查询）
            if (resultArray.length > 1) {
              // 处理多系列数据（如状态码分布）
              const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2'];
              const processedSeries = resultArray.map((series: any, index: number) => {
                const data = series.values?.map(([timestamp, value]: [number, string]) => [
                  timestamp * 1000,
                  parseFloat(value) || 0
                ]) || [];

                // 直接使用Prometheus metric对象中的原始标签值作为系列名称
                // 不进行任何标签清理、服务名提取或前缀移除处理
                let seriesName = `Series ${index + 1}`;
                if (series.metric) {
                  const keys = Object.keys(series.metric);
                  if (keys.length > 0) {
                    if (keys.length === 1) {
                      // 单标签情况：直接使用标签值
                      const key = keys[0];
                      const rawValue = series.metric[key];
                      seriesName = rawValue;
                    } else {
                      // 多标签情况：组合所有标签值，用 | 分隔
                      const labelParts = keys.map(key => series.metric[key]);
                      seriesName = labelParts.join(' | ');
                    }
                  }
                }

                console.log(`${title} 系列 ${index}:`, {
                  metric: series.metric,
                  seriesName: seriesName,
                  dataLength: data.length
                });

                return {
                  name: seriesName, // 直接使用原始标签值
                  originalName: seriesName,
                  data,
                  color: colors[index % colors.length]
                };
              });

              // 验证和清理数据，保持原始时间戳
              const cleanedSeriesData = validateAndCleanMultiSeriesData(processedSeries, timeRange);

              console.log(`${title} 数据处理完成:`, {
                原始系列数量: resultArray.length,
                处理后系列数量: cleanedSeriesData.length,
                系列名称: cleanedSeriesData.map(s => s.name)
              });

              setSeriesData(cleanedSeriesData);
              setChartData([]); // 清空单系列数据
              console.log(`${title} 最终多系列数据:`, cleanedSeriesData);
            } else if (resultArray[0]?.values) {
              // 处理单系列数据
              const processedData = resultArray[0].values.map(([timestamp, value]: [number, string]) => [
                timestamp * 1000,
                parseFloat(value) || 0
              ]);

              // 验证和清理数据，保持原始时间戳
              const cleanedData = validateAndCleanData(processedData, timeRange);
              setChartData(cleanedData);
              setSeriesData([]); // 清空多系列数据
              console.log(`${title} 处理后的单系列数据:`, cleanedData.slice(0, 3), '...');
            } else {
              console.log(`${title} 无数据，响应结构:`, res);
              setChartData([]);
              setSeriesData([]);
            }
          } else if (res?.result?.status === 'error') {
            console.error(`${title} 查询失败:`, res.result.error);
            setChartData([]);
            setSeriesData([]);
          } else {
            console.log(`${title} 无数据，响应结构:`, res);
            setChartData([]);
            setSeriesData([]);
          }
        }
      },
      onError: (error) => {
        console.error(`${title} 查询数据失败:`, error);
        setChartData([]);
      }
    }
  );

  // 格式化数值
  // 格式化数值 - 使用智能精度格式化
  const formatValue = useCallback((value: number | string | any) => {
    return formatValueWithUnit(value, unit);
  }, [unit]);

  // 初始化图表
  const initChart = useCallback(() => {
    if (!chartRef.current) return;

    // 销毁已存在的图表实例
    if (chartInstance.current) {
      chartInstance.current.dispose();
    }

    chartInstance.current = echarts.init(chartRef.current);

    const option = {
      title: {
        // text: title, // 暂时不显示标题，因为图表已经在顶部显示了
        left: 'left',
        textStyle: {
          fontSize: 14,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          if (params && params.length > 0) {
            // 使用数据点的实际时间戳，而不是X轴刻度时间
            const actualTimestamp = Array.isArray(params[0].value) ? params[0].value[0] : params[0].axisValue;
            const time = formatTooltipTime(actualTimestamp, timeRange);
            let content = `${time}<br/>`;

            // 遍历所有系列数据
            params.forEach((param: any) => {
              const value = Array.isArray(param.value) ? param.value[1] : param.value;
              const formattedValue = formatValue(value);
              const color = param.color;
              content += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
              content += `${param.seriesName}: ${formattedValue}<br/>`;
            });

            return content;
          }
          return '';
        }
      },
      legend: getOptimizedLegendConfig({
        seriesCount: seriesData.length,
        isModal: false,
        formatter: createDescriptiveFormatter(LEGEND_DESCRIPTIONS, 25),
        maxTextLength: 25,
        enableTooltip: true,
        originalDataMap: getOriginalDataMap(seriesData, LEGEND_DESCRIPTIONS)
      }),
      grid: getOptimizedGridConfig(seriesData.length, false),
      xAxis: (() => {
        const tickConfig = getXAxisTickConfig(timeRange);
        return {
          type: 'time',
          boundaryGap: false,
          // 使用固定的刻度点
          min: timeRange.start,
          max: timeRange.end,
          splitNumber: tickConfig.splitNumber,
          minInterval: tickConfig.minInterval,
          maxInterval: tickConfig.maxInterval,
          // 强制显示所有刻度标签
          axisLabel: {
            formatter: function(value: number) {
              return formatXAxisTime(value, timeRange);
            },
            showMinLabel: true,
            showMaxLabel: true,
            interval: 0 // 显示所有标签
          }
        };
      })(),
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: function(value: number) {
            return formatYAxisLabel(value, unit);
          }
        }
      },
      dataZoom: [
        {
          type: 'slider',
          show: true,
          xAxisIndex: [0],
          start: 0,
          end: 100,
          height: 30,
          bottom: 10,
          handleStyle: {
            color: '#1890ff'
          },
          textStyle: {
            color: '#1890ff'
          }
        }
      ],
      series: (() => {
        // 如果有多系列数据，使用多条折线配置
        if (seriesData.length > 0) {
          const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2'];
          return seriesData.map((series, index) => {
            const seriesConfig: any = {
              name: series.name,
              type: 'line',
              data: series.data,
              smooth: true,
              symbol: 'circle', // 显示圆形数据点
              symbolSize: 2, // 数据点大小
              lineStyle: {
                color: series.color || colors[index % colors.length],
                width: title.includes('状态码') ? 2 : 1 // 状态码分布使用更粗的线条
              }
            };

            // 根据图表模式添加面积图样式
            if (chartMode === 'area') {
              const color = series.color || colors[index % colors.length];
              const rgb = color.replace('#', '');
              const r = parseInt(rgb.substr(0, 2), 16);
              const g = parseInt(rgb.substr(2, 2), 16);
              const b = parseInt(rgb.substr(4, 2), 16);

              seriesConfig.areaStyle = {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    { offset: 0, color: `rgba(${r}, ${g}, ${b}, 0.3)` },
                    { offset: 1, color: `rgba(${r}, ${g}, ${b}, 0.1)` }
                  ]
                }
              };
            }

            return seriesConfig;
          });
        }

        // 单系列数据配置（向后兼容）
        const seriesConfig: any = {
          name: title,
          type: 'line',
          data: chartData,
          smooth: true,
          symbol: 'circle',
          symbolSize: 2,
          lineStyle: {
            color: '#1890ff',
            width: 2
          }
        };

        // 根据图表模式添加面积图样式
        if (chartMode === 'area') {
          seriesConfig.areaStyle = {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
              ]
            }
          };
        }

        return [seriesConfig];
      })()
    };

    chartInstance.current.setOption(option);

    // 设置图例工具提示
    if (seriesData.length > 0) {
      const originalDataMap = getOriginalDataMap(seriesData, LEGEND_DESCRIPTIONS, true); // 启用标签清理
      console.log('AlertChart main - Setting up tooltips with data:', originalDataMap);
      console.log('AlertChart main - Series data:', seriesData.map(s => ({
        display: s.name,
        original: s.originalName
      })));
      addSimpleLegendTooltip(chartInstance.current, originalDataMap, 25);
      createCustomLegendTooltip(chartInstance.current, originalDataMap, 25);
    }

    console.log(`${title} 图表初始化完成`);
  }, [title, chartData, seriesData, formatValue, timeRange, chartMode]);

  // 更新图表数据
  const updateChart = useCallback(() => {
    if (!chartInstance.current) return;

    // 如果有多系列数据，更新多条折线图
    if (seriesData.length > 0) {
      const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2'];
      const seriesConfig = seriesData.map((series, index) => ({
        name: series.name,
        type: 'line',
        data: series.data,
        smooth: true,
        symbol: 'circle',
        symbolSize: 2,
        // 所有图表都不使用堆叠和面积填充，使用普通折线
        // ...(title.includes('状态码') || title.includes('请求响应时间') ? {} : { stack: 'response-time' }),
        lineStyle: {
          color: series.color || colors[index % colors.length],
          width: title.includes('状态码') ? 2 : 1 // 状态码分布使用更粗的线条
        }
      }));
      chartInstance.current.setOption({ series: seriesConfig });
    } else {
      // 单系列数据更新
      chartInstance.current.setOption({
        series: [{ data: chartData }]
      });
    }
    console.log(`${title} 图表数据更新完成`);
  }, [title, chartData, seriesData]);

  // 打开弹窗
  const openModal = useCallback(() => {
    console.log(`${title} 打开弹窗`);
    setIsModalVisible(true);
  }, [title]);

  // 关闭弹窗
  const closeModal = useCallback(() => {
    console.log(`${title} 关闭弹窗`);
    // 清理弹窗中的图表实例
    if (modalChartInstance.current) {
      modalChartInstance.current.dispose();
      modalChartInstance.current = null;
    }
    setIsModalVisible(false);
  }, [title]);

  // 监听数据变化，触发查询
  useEffect(() => {
    const hasQuery = query || (queries && queries.length > 0);
    if (allowQueries && instanceId && hasQuery && timeRange.start && timeRange.end) {
      console.log(`${title} 触发数据查询`);
      fetchData();
    } else {
      console.log(`${title} 跳过数据查询，缺少必要参数或查询被禁止:`, {
        allowQueries,
        instanceId: !!instanceId,
        hasQuery: !!hasQuery,
        timeRange: !!(timeRange.start && timeRange.end)
      });
      // 清空数据
      setChartData([]);
      setSeriesData([]);
    }
  }, [allowQueries, instanceId, query, queries, timeRange.start, timeRange.end, refreshTrigger, fetchData, title]);

  // 初始化图表
  useEffect(() => {
    initChart();
    
    // 窗口大小变化时重新调整图表
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartInstance.current) {
        chartInstance.current.dispose();
      }
    };
  }, [initChart]);

  // 数据变化时更新图表
  useEffect(() => {
    if (chartInstance.current && (chartData.length > 0 || seriesData.length > 0)) {
      updateChart();
    }
  }, [chartData, seriesData, updateChart]);

  // 初始化弹窗中的图表
  const initModalChart = useCallback(() => {
    console.log(`${title} 初始化弹窗图表`, {
      modalChartRef: !!modalChartRef.current,
      chartDataLength: chartData.length,
      seriesDataLength: seriesData.length
    });

    if (!modalChartRef.current) {
      console.warn(`${title} 弹窗图表容器不存在`);
      return;
    }

    if (chartData.length === 0 && seriesData.length === 0) {
      console.warn(`${title} 弹窗图表无数据`);
      return;
    }

    if (modalChartInstance.current) {
      modalChartInstance.current.dispose();
    }

    modalChartInstance.current = echarts.init(modalChartRef.current);
    console.log(`${title} 弹窗图表实例创建成功`);

    // 使用与主图表相同的配置，但调整尺寸
    const option = {
      title: {
        // text: title,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          if (params && params.length > 0) {
            // 使用数据点的实际时间戳，而不是X轴刻度时间
            const actualTimestamp = Array.isArray(params[0].value) ? params[0].value[0] : params[0].axisValue;
            const time = formatTooltipTime(actualTimestamp, timeRange);
            let content = `${title}<br/>${time}<br/>`;

            // 遍历所有系列数据
            params.forEach((param: any) => {
              const value = Array.isArray(param.value) ? param.value[1] : param.value;
              const formattedValue = formatValue(value);
              const color = param.color;
              content += `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
              content += `${param.seriesName}: ${formattedValue}<br/>`;
            });

            return content;
          }
          return '';
        }
      },
      legend: getOptimizedLegendConfig({
        seriesCount: seriesData.length,
        isModal: true,
        formatter: createDescriptiveFormatter(LEGEND_DESCRIPTIONS, 35),
        maxTextLength: 35,
        enableTooltip: true,
        originalDataMap: getOriginalDataMap(seriesData, LEGEND_DESCRIPTIONS)
      }),
      grid: getOptimizedGridConfig(seriesData.length, true),
      xAxis: (() => {
        const tickConfig = getXAxisTickConfig(timeRange);
        return {
          type: 'time',
          boundaryGap: false,
          min: timeRange.start,
          max: timeRange.end,
          splitNumber: tickConfig.splitNumber,
          minInterval: tickConfig.minInterval,
          maxInterval: tickConfig.maxInterval,
          axisLabel: {
            formatter: function(value: number) {
              return formatXAxisTime(value, timeRange);
            },
            showMinLabel: true,
            showMaxLabel: true,
            interval: 0 // 显示所有标签
          }
        };
      })(),
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: function(value: number) {
            return formatYAxisLabel(value, unit);
          }
        }
      },
      dataZoom: [{
        type: 'slider',
        show: true,
        xAxisIndex: [0],
        start: 0,
        end: 100,
        height: 40,
        bottom: 20
      }],
      series: (() => {
        // 如果有多系列数据，使用多条折线配置
        if (seriesData.length > 0) {
          const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2'];
          return seriesData.map((series, index) => {
            const seriesConfig: any = {
              name: series.name,
              type: 'line',
              data: series.data,
              smooth: true,
              symbol: 'circle', // 显示圆形数据点
              symbolSize: 2, // 数据点大小
              lineStyle: {
                color: series.color || colors[index % colors.length],
                width: title.includes('状态码') ? 2 : 1 // 状态码分布使用更粗的线条
              }
            };

            // 根据图表模式添加面积图样式
            if (chartMode === 'area') {
              const color = series.color || colors[index % colors.length];
              const rgb = color.replace('#', '');
              const r = parseInt(rgb.substr(0, 2), 16);
              const g = parseInt(rgb.substr(2, 2), 16);
              const b = parseInt(rgb.substr(4, 2), 16);

              seriesConfig.areaStyle = {
                color: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    { offset: 0, color: `rgba(${r}, ${g}, ${b}, 0.3)` },
                    { offset: 1, color: `rgba(${r}, ${g}, ${b}, 0.1)` }
                  ]
                }
              };
            }

            return seriesConfig;
          });
        }

        // 单系列数据配置
        const seriesConfig: any = {
          name: title,
          type: 'line',
          data: chartData,
          smooth: true,
          symbol: 'none',
          lineStyle: {
            width: 2,
            color: '#2468F2'
          }
        };

        // 根据图表模式添加面积图样式
        if (chartMode === 'area') {
          seriesConfig.areaStyle = {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(36, 104, 242, 0.3)' },
                { offset: 1, color: 'rgba(36, 104, 242, 0.1)' }
              ]
            }
          };
        }

        return [seriesConfig];
      })()
    };

    modalChartInstance.current.setOption(option);

    // 设置弹窗图例工具提示
    if (seriesData.length > 0) {
      const originalDataMap = getOriginalDataMap(seriesData, LEGEND_DESCRIPTIONS, true); // 启用标签清理
      console.log('AlertChart modal - Setting up tooltips with data:', originalDataMap);
      console.log('AlertChart modal - Series data:', seriesData.map(s => ({
        display: s.name,
        original: s.originalName
      })));
      addSimpleLegendTooltip(modalChartInstance.current, originalDataMap, 35);
      createCustomLegendTooltip(modalChartInstance.current, originalDataMap, 35);
    }

    console.log(`${title} 弹窗图表配置设置完成`, {
      seriesCount: Array.isArray(option.series) ? option.series.length : 0
    });
  }, [title, chartData, seriesData, formatValue, timeRange]);

  // 当弹窗打开时初始化图表
  useEffect(() => {
    if (isModalVisible) {
      console.log(`${title} 弹窗打开，准备初始化图表`);

      // 增加延迟，确保DOM完全渲染
      const timer = setTimeout(() => {
        initModalChart();

        // 如果初始化失败，再次尝试
        setTimeout(() => {
          if (modalChartRef.current && !modalChartInstance.current) {
            console.log(`${title} 重试初始化弹窗图表`);
            initModalChart();
          }
        }, 200);
      }, 200);

      return () => clearTimeout(timer);
    }
  }, [isModalVisible, initModalChart]);

  // 弹窗中图表的resize处理
  useEffect(() => {
    if (isModalVisible && modalChartInstance.current) {
      const handleModalResize = () => {
        if (modalChartInstance.current) {
          modalChartInstance.current.resize();
        }
      };

      window.addEventListener('resize', handleModalResize);
      return () => {
        window.removeEventListener('resize', handleModalResize);
      };
    }
  }, [isModalVisible]);

  return (
    <>
      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles.titleSection}>
            <div className={styles.title}>
              {title}
              {description && (
                <HelpTooltip
                  content={description}
                />
              )}
            </div>
          </div>
          <div className={styles.actions}>
            <Button
              type="text"
              icon={<OutlinedArrowsAlt />}
              onClick={openModal}
            />
          </div>
        </div>
        <div
          ref={chartRef}
          className={styles.chart}
          style={{
            height: '300px',
            opacity: loading ? 0.6 : 1
          }}
        />
        {loading && (
          <div className={styles.loading}>
            数据加载中...
          </div>
        )}
        {!loading && chartData.length === 0 && seriesData.length === 0 && instanceId && (
          <div className={styles.noData}>
            暂无数据
          </div>
        )}
        {!instanceId && (
          <div className={styles.noData}>
            请选择网关实例
          </div>
        )}
      </div>

      {/* 弹窗显示放大的图表 */}
      <Modal
        title={title}
        visible={isModalVisible}
        onCancel={closeModal}
        footer={null}
        destroyOnClose={true}
        width='1000px'
        style={{ height: '70vh', overflow: 'hidden' }}
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
            width: '100%'
          }}
        >
          <div
            ref={modalChartRef}
            style={{
              height: '80%', // 图表高度缩小到80%
              width: '100%'   // 图表宽度缩小到80%
            }}
          />
        </div>
      </Modal>
    </>
  );
};

export default AlertChart;
