# AI网关控制台 (AIGW Console)

AI网关控制台是一个用于管理和监控AI网关服务的Web应用程序，提供实时监控、告警管理、路由配置等功能。

## 最新更新

### 2024-12-19 监控面板功能优化
- ✅ **新增监控面板帮助图标功能**
  - 在每个监控面板标题旁添加问号帮助图标
  - 鼠标悬停显示现有的指标描述信息
  - 将原本显示在标题下方的描述文本移到帮助图标的Tooltip中
  - 支持所有监控维度：网关、路由、服务

- ✅ **修复请求响应时间面板显示问题**
  - 修复服务维度请求响应时间面板的多百分位数显示
  - 每个服务现在正确显示P50、P90、P99、平均值四条数据线
  - 回滚网关维度请求响应时间面板到聚合查询，避免显示异常
  - 优化多查询处理逻辑，支持每个查询返回多个时间序列
  - 图例清楚标识每条线对应的服务名称和百分位数类型
  - 支持Pod维度查询：仅在服务维度选择特定服务后自动切换到按destination_address分组
  - 修复Pod维度查询中的unknown过滤问题，为所有Pod维度查询添加destination_address!="unknown"过滤条件
  - 修复网关维度标签显示问题：确保网关维度显示聚合指标，不显示服务名前缀

- ✅ **简化的实现方案**
  - 使用现有的 `description` 字段作为帮助内容
  - 删除标题下方的描述文本显示
  - 创建简洁的 `HelpTooltip` 组件
  - 保持界面简洁，减少视觉干扰

### 2024-12-19 监控告警页面修复
- ✅ **修复Token相关指标图例标签项数量与实际数据线条数量不匹配问题**
  - 实现原始标签值显示，确保1:1对应关系
  - 支持多标签查询的正确处理（如 `sum by (ai_cluster, ai_consumer)`）
  - 修复消费者Token统计面板5条数据线只显示3个图例项的问题

- ✅ **修复服务维度TCP连接数显示异常问题**
  - 显示原始 `cluster_name` 值，不进行任何转换
  - 确保每个服务显示为独立的数据线
  - 保持服务名称的原始格式

- ✅ **代码优化和测试工具**
  - 添加监控修复测试工具到window对象
  - 添加原始标签值测试工具
  - 完善错误处理和日志记录

## 开发和构建

相关运行命令如下：

```shell
# 本地联调沙盒环境
npm run dev

# 生产环境构建
npm run build
```

## 帮助图标功能说明

### 功能概述
监控告警页面的每个指标面板标题旁都有一个帮助图标（问号图标），用户可以通过鼠标悬停查看该指标的描述信息。

### 使用方法
1. 在监控告警页面，找到任意监控面板
2. 将鼠标悬停在面板标题右侧的问号图标上
3. 查看弹出的Tooltip，了解指标的描述信息

### 功能特点
- **简洁设计**：将原本显示在标题下方的描述文本移到帮助图标中
- **节省空间**：减少页面视觉干扰，保持界面简洁
- **易于使用**：鼠标悬停即可查看详细信息
- **统一体验**：所有监控面板都有一致的帮助功能

### 技术实现
- **组件位置**：`src/components/HelpTooltip/`
- **使用的UI组件**：ACUD的Tooltip组件，trigger="hover"
- **图标**：OutlinedQuestionCircle from acud-icon
- **内容来源**：使用现有的 `description` 字段

## 请求响应时间面板修复详情

### 问题描述
原有的请求响应时间面板存在以下问题：
1. **服务维度**：无法正确显示每个服务的多个百分位数指标
2. **网关维度**：只显示聚合数据，无法按Pod分组显示

### 修复方案

#### 1. 服务维度修复
**修改文件**：`src/pages/MonitorAlert/index.tsx`
- 保持原有的多查询配置（P50、P90、P99、Avg）
- 每个查询都按 `destination_service_name` 分组
- 确保每个服务的每个百分位数都有独立的数据线

#### 2. 网关维度修复
**修改文件**：`src/pages/MonitorAlert/index.tsx`
- 修改查询从聚合模式改为按 `source_workload` 分组
- 显示不同Pod/Workload的响应时间分布
- 支持P50、P90、P99、平均值四个指标

#### 3. 多查询处理逻辑优化
**修改文件**：`src/pages/MonitorAlert/components/AlertChart.tsx`
- 优化多查询响应处理逻辑
- 支持每个查询返回多个时间序列
- 构建系列名称格式：`服务名 + 百分位数`（如：service-a P50）
- 确保图例和数据线的正确对应关系

### Pod维度查询逻辑
**重要**：Pod维度功能仅在服务维度下生效，当用户选择特定服务（非"全部"）后，系统会自动进行以下转换：

#### 查询转换规则
1. **分组字段转换**：
   - 从 `sum by(destination_service_name, le)` 转换为 `sum by(destination_address, le)`
   - 从 `sum by(destination_service_name)` 转换为 `sum by(destination_address)`

2. **过滤条件优化**：
   - 添加服务名模糊匹配：`destination_service_name=~".*${selectedService}.*"`
   - 过滤unknown地址：`destination_address!="unknown"`（新增）
   - 保持命名空间过滤：`user_namespace="${namespace}"`

#### 实现细节
- **条件限制**：只有在 `selectedDimension === 'service' && selectedService !== '全部'` 时才进行转换
- **百分位数查询**：处理 `histogram_quantile` 函数的查询转换
- **平均值查询**：处理 `istio_request_duration_milliseconds_sum/count` 的查询转换
- **自动识别**：根据查询语句特征自动应用相应的转换规则
- **Unknown过滤**：为所有Pod维度查询统一添加 `destination_address!="unknown"` 过滤条件

### 最终效果
- **网关维度**：显示整体聚合的指标，标签不带服务名前缀（如"请求成功率"而非"Service1 请求成功率"）
- **服务维度**：每个服务显示独立的指标线，标签带服务名标识
- **Pod维度**：仅在服务维度选择特定服务后生效，显示该服务下各Pod的详细指标
- **图例清晰**：明确标识每条线对应的服务/Pod和指标类型
- **数据完整**：确保所有时间序列都有对应的图例项
- **过滤准确**：正确过滤unknown地址，只显示有效的Pod数据
- **维度隔离**：网关维度不受Pod功能影响，保持原有聚合显示逻辑

## 关键问题修复

### 网关维度标签显示问题
**问题描述**：网关维度下的指标标签错误地显示了服务名前缀（如"Service1 请求成功率"），应该显示聚合的整体指标（如"请求成功率"）。

**根本原因**：查询处理逻辑中存在条件判断错误，导致网关维度的查询被错误地应用了服务维度的过滤逻辑。

**修复方案**：
1. **精确条件匹配**：将过于宽泛的条件 `query.includes('destination_service_name')` 改为精确匹配 `query.includes('sum by (destination_service_name)')`
2. **维度隔离**：确保只有在服务维度且选择了具体服务时才应用Pod维度转换逻辑
3. **查询保护**：网关维度的聚合查询不受任何服务过滤逻辑影响

**修复前**（错误逻辑）：
```typescript
// 这会匹配所有包含destination_service_name的查询，包括网关维度的过滤条件
else if (query.includes('destination_service_name') || query.includes('destination_address')) {
  // 错误地为网关维度查询添加服务过滤
}
```

**修复后**（正确逻辑）：
```typescript
// 只匹配真正需要按服务分组的查询
else if (query.includes('sum by (destination_service_name)') || query.includes('sum by(destination_service_name)')) {
  // 只为服务维度的分组查询添加过滤
}
```

**AlertChart组件修复**：
修复了图表组件中系列名称生成逻辑，确保聚合查询（无标签）只显示查询名称，而不添加服务名前缀：

```typescript
// 修复前：总是添加服务名前缀
const seriesName = `${serviceName} ${queryRes.queryName}`;

// 修复后：根据是否有标签决定显示格式
const seriesName = hasLabels ? `${serviceName} ${queryRes.queryName}` : queryRes.queryName;
```

**网关维度请求成功数查询修复**：
修复了网关维度"请求成功数"指标缺少聚合函数的问题，避免显示多条线：

```typescript
// 修复前：缺少聚合函数，返回多个时间序列
query: `increase(istio_requests_total{
  reporter="source",
  response_code=~"2..",
  user_namespace="$namespace"
}[5m])`

// 修复后：使用sum()聚合，返回单个时间序列
query: `sum(increase(istio_requests_total{
  reporter="source",
  response_code=~"2..",
  user_namespace="$namespace"
}[5m]))`
```

**Token指标按服务名称聚合修复**：
修复了Token相关指标中ai_cluster标签格式导致的聚合问题。原始ai_cluster值格式为：
`"outbound|80|default-ollama-service-v1|ollama-service-v1.ollama.svc.cluster.local"`

问题：相同服务的不同路由被分别显示，无法按服务聚合。

解决方案：使用Prometheus的`label_replace`函数从ai_cluster中提取服务名称：

```typescript
// 修复前：按完整ai_cluster值分组
query: `sum by (ai_cluster) (
  route_upstream_model_consumer_metric_input_token{user_namespace="$namespace"}
)`

// 修复后：提取服务名称并按服务聚合
query: `sum by (service_name) (
  label_replace(
    route_upstream_model_consumer_metric_input_token{user_namespace="$namespace"},
    "service_name", "$1", "ai_cluster", "outbound\\|[^|]*\\|[^|]*\\|([^.]*)\\..*"
  )
)`
```

**修复范围**：
- 服务维度：输入/输出Token消耗总数、Token每秒消耗数
- 消费者Token使用统计（输入/输出）
- 服务Token使用统计（输入/输出）
- 模型Token使用统计（输入/输出）
- 路由维度的服务Token使用统计

**正则表达式说明**：
`"outbound\\|[^|]*\\|[^|]*\\|([^.]*)\\..*"` 用于提取服务名称：
- `outbound\\|` - 匹配"outbound|"前缀
- `[^|]*\\|` - 跳过端口部分
- `[^|]*\\|` - 跳过路由名称部分
- `([^.]*)` - 捕获服务名称（到第一个点之前）
- `\\..*` - 匹配剩余的域名部分
